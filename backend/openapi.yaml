openapi: 3.0.3
info:
  title: Choreo Task Management API
  version: 1.0.0
  description: API for task management with user authentication.
servers:
  - url: http://localhost:8080

paths:
  /:
    get:
      summary: Root Endpoint
      description: Root endpoint that provides API information and status.
      responses:
        '200':
          description: API information and status
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Choreo Task Management API"
                  version:
                    type: string
                    example: "1.0.0"
                  status:
                    type: string
                    example: "running"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-07-09T15:30:00.123Z"

  /health:
    get:
      summary: Health Check
      description: A simple endpoint to verify that the service is running.
      responses:
        '200':
          description: Service is available
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "healthy"
                  timestamp:
                    type: string
                    format: date-time
                    example: "2025-07-09T15:30:00.123Z"

  /api/tasks:
    get:
      summary: Get Tasks
      description: Retrieve all tasks for the authenticated user.
      responses:
        '200':
          description: Tasks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  tasks:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          example: "123e4567-e89b-12d3-a456-426614174000"
                        title:
                          type: string
                          example: "Complete project documentation"
                        description:
                          type: string
                          example: "Write comprehensive documentation for the project"
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Invalid request"

    post:
      summary: Create Task
      description: Create a new task for the authenticated user.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  example: "Complete project documentation"
                description:
                  type: string
                  example: "Write comprehensive documentation for the project"
              required:
                - title
      responses:
        '200':
          description: Task created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Task created successfully"
                  task:
                    type: object
                    properties:
                      id:
                        type: string
                        example: "123e4567-e89b-12d3-a456-426614174000"
                      title:
                        type: string
                        example: "Complete project documentation"
                      description:
                        type: string
                        example: "Write comprehensive documentation for the project"
        '400':
          description: Bad Request - Required fields missing
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Title is required"

